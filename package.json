{"name": "anithing.moe", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "format": "prettier --write .", "lint": "prettier --check ."}, "devDependencies": {"@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "tailwindcss": "^4.0.0", "vite": "^6.2.6"}, "dependencies": {"@ethercorps/sveltekit-og": "^3.0.0", "@getbrevo/brevo": "^2.2.0", "@sveltejs/adapter-node": "^5.2.12", "dotenv": "^16.5.0", "svelte-typewrite": "^3.0.0"}}