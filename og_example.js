import { ImageResponse } from '@ethercorps/sveltekit-og';
import { error } from '@sveltejs/kit';

const CACHE_CONTROL = 'public, max-age=604800, stale-while-revalidate=86400';

function generateCacheKey(params) {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');

  return Buffer.from(sortedParams).toString('base64').replace(/[/+=]/g, '');
}

export const GET = async ({ url, setHeaders }) => {
  try {
    // Parameters for the main page OG image
    const params = {
      title: 'anithing.moe',
      subtitle: 'Your ultimate gateway to Japanese media',
      description: 'Search, track, and manage anime, manga, VNs, and LNs across all platforms',
      type: url.searchParams.get('type') || 'home'
    };

    // Generate cache key and set headers
    const cacheKey = generateCacheKey(params);
    setHeaders({
      'Cache-Control': CACHE_CONTROL,
      'ETag': `"${cacheKey}"`,
      'Content-Type': 'image/png'
    });

    // Load font (using the same font from the example)
    const fontFile = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
    if (!fontFile.ok) {
      throw error(500, 'Failed to load font');
    }
    const fontData = await fontFile.arrayBuffer();

    const template = `
    <div tw="flex w-full h-full relative overflow-hidden">
      <!-- Gradient Background -->
      <div tw="absolute w-full h-full bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900"></div>
      
      <!-- Animated gradient blobs -->
      <div tw="absolute top-[-10%] left-[-10%] w-[600px] h-[600px] bg-purple-600 rounded-full opacity-30" style="filter: blur(120px);"></div>
      <div tw="absolute bottom-[-10%] right-[-10%] w-[500px] h-[500px] bg-sky-500 rounded-full opacity-30" style="filter: blur(100px);"></div>
      <div tw="absolute top-[20%] right-[5%] w-[300px] h-[300px] bg-pink-500 rounded-full opacity-20" style="filter: blur(80px);"></div>

      <!-- Content Container -->
      <div tw="flex flex-col w-full h-full px-24 py-16 justify-center items-center relative z-10">
        
        <!-- Main Title -->
        <div tw="flex flex-col items-center mb-8">
          <h1 tw="text-white text-8xl font-bold mb-4 text-center">
            anithing.moe
          </h1>
          <div tw="w-32 h-1 bg-gradient-to-r from-purple-500 via-sky-400 to-pink-500 rounded-full"></div>
        </div>

        <!-- Subtitle -->
        <h2 tw="text-sky-300 text-4xl font-semibold mb-6 text-center max-w-4xl">
          ${params.subtitle}
        </h2>

        <!-- Description -->
        <p tw="text-slate-300 text-2xl text-center max-w-5xl leading-relaxed mb-8">
          ${params.description}
        </p>

        <!-- Feature badges -->
        <div tw="flex flex-wrap justify-center gap-4 mt-8">
          <div tw="flex items-center px-6 py-3 bg-purple-600/30 rounded-full border border-purple-400/30">
            <span tw="text-purple-300 text-xl font-medium">🔍 Unified Search</span>
          </div>
          <div tw="flex items-center px-6 py-3 bg-sky-600/30 rounded-full border border-sky-400/30">
            <span tw="text-sky-300 text-xl font-medium">📚 Centralized Lists</span>
          </div>
          <div tw="flex items-center px-6 py-3 bg-pink-600/30 rounded-full border border-pink-400/30">
            <span tw="text-pink-300 text-xl font-medium">👀 Track Friends</span>
          </div>
        </div>

        <!-- Bottom accent -->
        <div tw="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div tw="text-slate-400 text-lg">All your media, aniwhere.</div>
        </div>
      </div>
    </div>
    `;

    const imageResponse = await new ImageResponse(template, {
      width: 1200,
      height: 630,
      fonts: [
        {
          name: 'Gilroy-SemiBold',
          data: fontData,
          weight: 600,
          style: 'normal'
        }
      ],
      headers: {
        'Cache-Control': CACHE_CONTROL,
        'ETag': `"${cacheKey}"`
      }
    });

    return imageResponse;

  } catch (e) {
    console.error('Error generating OG image:', e);

    // If it's already an error response from our validation, pass it through
    if (e.status === 400) {
      throw e;
    }

    // Otherwise return a generic error
    throw error(500, 'Failed to generate image');
  }
};
